.search-filter-container {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.search-section {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 200px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  background: white;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.search-btn, .clear-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.search-btn {
  background: #4CAF50;
  color: white;
}

.search-btn:hover {
  background: #45a049;
  transform: translateY(-2px);
}

.clear-btn {
  background: #f44336;
  color: white;
}

.clear-btn:hover {
  background: #da190b;
  transform: translateY(-2px);
}

.filter-section {
  display: flex;
  align-items: center;
}

.notification-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: white;
  font-weight: 500;
}

.notification-toggle input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #4CAF50;
}

.toggle-label {
  font-size: 1rem;
}

@media (max-width: 768px) {
  .search-section {
    flex-direction: column;
  }
  
  .search-input {
    min-width: auto;
  }
}
