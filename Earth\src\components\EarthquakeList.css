.earthquake-list {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  height: 600px;
  overflow-y: auto;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

.list-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
}

.list-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.sort-select, .filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  color: #333;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sort-select:hover, .filter-select:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.sort-select:focus, .filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.earthquake-count {
  background: #f8f9fa;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 0.9rem;
  color: #666;
  border-left: 4px solid #667eea;
}

.filter-indicator {
  color: #667eea;
  font-weight: 500;
}

.earthquakes-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.earthquake-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  background: #f8f9fa;
}

.earthquake-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.earthquake-item.selected {
  border-color: #667eea;
  background: #f0f4ff;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
}

.earthquake-magnitude {
  flex-shrink: 0;
}

.magnitude-value {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.earthquake-details {
  flex: 1;
  min-width: 0;
}

.earthquake-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  font-size: 1rem;
  line-height: 1.3;
}

.earthquake-location {
  color: #666;
  margin-bottom: 4px;
  font-size: 0.9rem;
  line-height: 1.3;
}

.earthquake-time {
  color: #999;
  font-size: 0.8rem;
  font-weight: 500;
}

.no-earthquakes {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.no-earthquakes-icon {
  font-size: 3rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-earthquakes-text {
  font-size: 1rem;
  line-height: 1.5;
}

.no-earthquakes-text > div {
  margin-top: 8px;
  font-size: 0.9rem;
  color: #999;
}

@media (max-width: 768px) {
  .earthquake-list {
    height: 400px;
    padding: 16px;
  }
  
  .list-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .list-controls {
    justify-content: center;
  }
  
  .earthquake-item {
    padding: 12px;
    gap: 12px;
  }
  
  .magnitude-value {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .earthquake-title {
    font-size: 0.9rem;
  }
  
  .earthquake-location {
    font-size: 0.8rem;
  }
}
