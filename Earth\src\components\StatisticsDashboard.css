.statistics-dashboard {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.dashboard-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dashboard-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.time-range-select {
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-range-select:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.export-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.export-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.9;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 4px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.chart-container {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.chart-container h4 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.magnitude-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.magnitude-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bar-label {
  min-width: 60px;
  font-size: 0.9rem;
  font-weight: 500;
}

.bar-container {
  flex: 1;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 10px;
  transition: width 0.5s ease;
  min-width: 4px;
}

.bar-count {
  min-width: 30px;
  text-align: right;
  font-weight: 600;
  font-size: 0.9rem;
}

.regions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.region-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.region-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(4px);
}

.region-name {
  font-weight: 500;
}

.region-count {
  background: rgba(255, 255, 255, 0.3);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.strongest-earthquake {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.strongest-earthquake h4 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.strongest-details {
  display: flex;
  align-items: center;
  gap: 20px;
}

.strongest-magnitude {
  font-size: 3rem;
  font-weight: 800;
  color: #ff4444;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  min-width: 80px;
}

.strongest-info {
  flex: 1;
}

.strongest-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.strongest-location {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-bottom: 4px;
}

.strongest-time {
  font-size: 0.8rem;
  opacity: 0.7;
}

@media (max-width: 768px) {
  .statistics-dashboard {
    padding: 16px;
  }
  
  .dashboard-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .dashboard-controls {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .stat-card {
    padding: 16px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .charts-section {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .strongest-details {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
}
